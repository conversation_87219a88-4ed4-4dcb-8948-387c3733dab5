"use client";

import React, { useState, useRef, useEffect, useCallback } from "react";
import { BootstrapTooltip } from "@/components/UIComponents/ToolTip/Tooltip-material-ui";
import LockedTabIndicator from "@/components/UIComponents/LockedTabIndicator";
import { ChevronDown } from "lucide-react";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { createPortal } from "react-dom";

/**
 * ProfessionalTabsWithOverflow - A clean, rectangular tab navigation component with overflow handling
 *
 * @param {Object} props
 * @param {Array} props.tabs - Array of tab objects with name, label, icon, and tooltip
 * @param {string} props.currentTab - The currently active tab name
 * @param {Function} props.handleTabChange - Function to call when a tab is clicked
 * @param {boolean} props.isFreeUser - Whether the user is on a free plan
 * @param {Array} props.lockedTabs - Array of tab names that are locked for free users
 * @returns {JSX.Element}
 */
const ProfessionalTabsWithOverflow = ({
  tabs,
  currentTab,
  handleTabChange,
  isFreeUser = false,
  lockedTabs = [],

}) => {
  const [visibleTabs, setVisibleTabs] = useState(tabs);
  const [overflowTabs, setOverflowTabs] = useState([]);
  const [isOverflowMenuOpen, setIsOverflowMenuOpen] = useState(false);
  const containerRef = useRef(null);
  const tabRefs = useRef([]);

  // Measure the width of each tab
  const measureTabWidths = useCallback(() => {
    return tabRefs.current.map(ref => {
      if (!ref) return 120; // Default width estimate
      const rect = ref.getBoundingClientRect();
      return Math.ceil(rect.width) + 8; // Add a small buffer
    });
  }, []);

  // Calculate which tabs should be visible and which should overflow
  const calculateVisibleTabs = useCallback(() => {
    if (!containerRef.current || !tabRefs.current.length) return;

    const containerWidth = containerRef.current.clientWidth;
    const moreButtonWidth = 100; // Estimated width of the "More" dropdown button
    const tabWidths = measureTabWidths();

    let remainingWidth = containerWidth - moreButtonWidth;
    const fittingTabs = [];
    const extraTabs = [];

    // Determine which tabs fit in the available space
    for (let i = 0; i < tabs.length; i++) {
      const tabWidth = tabWidths[i] || 120; // Use measured width or fallback

      if (remainingWidth >= tabWidth) {
        fittingTabs.push(tabs[i]);
        remainingWidth -= tabWidth;
      } else {
        extraTabs.push(tabs[i]);
      }
    }

    // If all tabs fit, don't show the overflow menu
    if (extraTabs.length === 0) {
      setVisibleTabs(tabs);
      setOverflowTabs([]);
    } else {
      setVisibleTabs(fittingTabs);
      setOverflowTabs(extraTabs);
    }
  }, [tabs, measureTabWidths]);

  // Recalculate on resize or tab changes
  useEffect(() => {
    calculateVisibleTabs();

    const handleResize = () => calculateVisibleTabs();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [calculateVisibleTabs, tabs]);

  // Render a single tab
  const renderTab = (tab, index) => {
    const isLockedTab = isFreeUser  && lockedTabs.includes(tab.name);
    const isActive = currentTab === tab.name;

    return (
      <BootstrapTooltip
        key={tab.name}
        title={isLockedTab ? "Premium feature - Upgrade to access" : tab?.tooltip}
        placement="bottom"
      >
        <button
          ref={(el) => { tabRefs.current[index] = el; }}
          onClick={() => !isLockedTab && handleTabChange(tab.name)}
          disabled={isLockedTab}
          className={`
            flex items-center gap-1.5 px-4 h-8
            transition-all duration-200 ease-in-out
            border-b-2 font-weight-medium typography-body-sm
            relative overflow-hidden
            ${isActive
              ? "border-primary text-primary bg-background"
              : "border-transparent text-muted-foreground hover:text-foreground hover:bg-muted/20"}
            ${isLockedTab ? "opacity-60 cursor-not-allowed" : "cursor-pointer"}
          `}
        >
          {/* Icon */}
          <div className={`
            flex items-center justify-center w-4 h-4 transition-colors duration-200
            ${isActive ? "text-primary" : "text-muted-foreground"}
          `}>
            {React.cloneElement(tab.icon, {
              className: "w-4 h-4",
              strokeWidth: isActive ? 2.5 : 2
            })}
          </div>

          {/* Label */}
          <span className={`
            font-weight-medium whitespace-nowrap transition-colors duration-200
            ${isActive ? "text-foreground" : "text-muted-foreground"}
          `}>
            {tab.label}
          </span>

          {/* Lock indicator for premium features */}
          {isLockedTab && (
            <div className="ml-1">
              <LockedTabIndicator />
            </div>
          )}
        </button>
      </BootstrapTooltip>
    );
  };

  return (
    <div
      ref={containerRef}
      className="flex w-full bg-background border-b border-border"
    >
      <div className="flex items-center overflow-x-hidden">
        {/* Render visible tabs */}
        {visibleTabs.map((tab, index) => renderTab(tab, index))}

        {/* Overflow dropdown */}
        {overflowTabs.length > 0 && (
          <DropdownMenu.Root
            open={isOverflowMenuOpen}
            onOpenChange={setIsOverflowMenuOpen}
          >
            <DropdownMenu.Trigger asChild>
              <button className="flex items-center gap-1.5 px-4 h-8 text-muted-foreground hover:text-foreground hover:bg-muted/20 border-b-2 border-transparent transition-all duration-200">
                <span className="font-weight-medium typography-body-sm">More</span>
                <ChevronDown className="w-4 h-4" />
              </button>
            </DropdownMenu.Trigger>

            {createPortal(
              <DropdownMenu.Content className="bg-background border border-border rounded-md shadow-lg p-1 z-50 mt-1">
                {overflowTabs.map((tab) => {
                  const isLockedTab = isFreeUser && lockedTabs.includes(tab.name);
                  const isActive = currentTab === tab.name;

                  return (
                    <BootstrapTooltip
                      key={tab.name}
                      title={isLockedTab ? "Premium feature - Upgrade to access" : tab?.tooltip}
                      placement="right"
                    >
                      <DropdownMenu.Item
                        onSelect={() => {
                          if (!isLockedTab) {
                            handleTabChange(tab.name);
                            setIsOverflowMenuOpen(false);
                          }
                        }}
                        className={`
                          flex items-center gap-2 px-3 py-2 typography-body-sm rounded-md transition-colors duration-200
                          ${isActive ? "bg-primary/10 text-primary" : "text-muted-foreground hover:bg-muted/50 hover:text-foreground"}
                          ${isLockedTab ? "opacity-60 cursor-not-allowed" : "cursor-pointer"}
                        `}
                      >
                        <div className={`w-4 h-4 transition-colors duration-200 ${isActive ? "text-primary" : "text-muted-foreground"}`}>
                          {React.cloneElement(tab.icon, { className: "w-4 h-4" })}
                        </div>
                        <span>{tab.label}</span>
                        {isLockedTab && <LockedTabIndicator />}
                      </DropdownMenu.Item>
                    </BootstrapTooltip>
                  );
                })}
              </DropdownMenu.Content>,
              document.body
            )}
          </DropdownMenu.Root>
        )}
      </div>
    </div>
  );
};

export default ProfessionalTabsWithOverflow;
