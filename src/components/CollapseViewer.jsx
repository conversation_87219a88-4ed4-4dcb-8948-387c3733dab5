import React, { useState, useContext, useEffect } from 'react';
import ChatPanel from './ChatPanel';
import { StateContext } from './Context/StateContext';
import { ExecutionContext } from './Context/ExecutionContext';
import { getFlowlineStatus } from '@/utils/api';
import ProjectTimeline from './ProjectTimeline';
import { useRouter, usePathname } from "next/navigation";
import { MessageSquare, ListTodo } from 'lucide-react';

const CollapseViewer = () => {
    const { activeLeftPanelTab, setActiveLeftPanelTab } = useContext(StateContext);
    const { projectStatus, setProjectStatus, isAutoConfigInProgress } = useContext(ExecutionContext);
    const [isLoading, setIsLoading] = useState(false);
    const [refresh, setRefresh] = useState(false);
    const router = useRouter();
    const pathname = usePathname();

    // Extract project ID from pathname
    const projectId = pathname.split('/')[3];

    // Set default tab to timeline
    useEffect(() => {
        if (!activeLeftPanelTab) {
            setActiveLeftPanelTab('timeline');
        }
    }, [activeLeftPanelTab, setActiveLeftPanelTab]);

    // Fetch project status
    useEffect(() => {
        const fetchProjectStatus = async () => {
            if (!projectId) return;
            
            setIsLoading(true);
            try {
                const response = await getFlowlineStatus(projectId);
                if (response && response.data) {
                    setProjectStatus(response.data);
                }
            } catch (error) {
                console.error('Error fetching project status:', error);
            } finally {
                setIsLoading(false);
            }
        };

        fetchProjectStatus();
    }, [projectId, refresh, setProjectStatus]);

    // Timeline skeleton component
    const TimelineSkeleton = () => {
        const skeletonItems = [
            { hasSubItems: true, subItemCount: 3 },
            { hasSubItems: true, subItemCount: 2 },
            { hasSubItems: false, subItemCount: 0 },
            { hasSubItems: true, subItemCount: 4 },
            { hasSubItems: false, subItemCount: 0 }
        ];

        return (
            <div className="w-full">
                <div className="sticky top-0 bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/75 z-10 flex justify-between items-center py-2 border-b">
                    <div className="flex-1">
                        <div className="h-4 bg-gray-200 rounded w-32 mx-4 animate-pulse"></div>
                    </div>
                    <div className="flex items-center gap-1 px-4">
                        <div className="h-3 bg-gray-200 rounded w-16 animate-pulse"></div>
                        <div className="w-4 h-4 bg-gray-200 rounded animate-pulse"></div>
                    </div>
                </div>

                <div className="max-w-2xl mx-auto px-6">
                    <div className="relative space-y-6">
                        {/* Timeline vertical line */}
                        <div className="absolute left-[21px] top-0 bottom-0 w-0.5 bg-gray-200"></div>

                        {/* Generate skeleton items */}
                        {skeletonItems.map((item, index) => (
                            <div key={index} className="relative">
                                {/* Timeline vertical line for each item */}
                                {index !== skeletonItems.length - 1 && (
                                    <div
                                        className="absolute left-[21px] w-0.5 bg-gray-200"
                                        style={{
                                            top: '2.5rem',
                                            height: item.hasSubItems ? `${(item.subItemCount * 3) + 3}rem` : '3rem',
                                            opacity: 1,
                                            visibility: 'visible',
                                        }}
                                    />
                                )}

                                {/* Main item skeleton */}
                                <div className="flex items-start space-x-4">
                                    {/* Icon skeleton */}
                                    <div className="flex-shrink-0 w-11 h-11 bg-gray-200 rounded-full animate-pulse"></div>

                                    {/* Content skeleton */}
                                    <div className="flex-grow min-w-0 pt-1.5">
                                        <div className="flex items-center justify-between mb-2">
                                            <div className="h-5 bg-gray-200 rounded w-32 animate-pulse"></div>
                                            <div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div>
                                        </div>
                                        <div className="h-4 bg-gray-200 rounded w-48 mb-3 animate-pulse"></div>

                                        {/* Sub-items skeleton */}
                                        {item.hasSubItems && (
                                            <div className="space-y-2 ml-4">
                                                {Array.from({ length: item.subItemCount }).map((_, subIndex) => (
                                                    <div key={subIndex} className="flex items-center space-x-3">
                                                        <div className="w-6 h-6 bg-gray-200 rounded animate-pulse"></div>
                                                        <div className="h-3 bg-gray-200 rounded w-24 animate-pulse"></div>
                                                        <div className="h-3 bg-gray-200 rounded w-12 animate-pulse"></div>
                                                    </div>
                                                ))}
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        );
    };

    const onRedirect = (action) => {
        const { buildProjectUrl } = require('@/utils/navigationHelpers');
        const organizationId = pathname.split('/')[1];
        const type = pathname.split('/')[2];

        switch (action) {
            case 'Add Repo':
                router.push(`${pathname}?ProjectAsset=code`);
                break;
            case 'Add Doc':
                router.push(`${pathname}?ProjectAsset=documents`);
                break;
            case 'SystemContext':
                router.push(buildProjectUrl(projectId, 'architecture/system-context'));
                break;
            case 'Container':
                router.push(buildProjectUrl(projectId, 'architecture/container'));
                break;
            case 'Component':
                router.push(buildProjectUrl(projectId, 'architecture/component'));
                break;
            case 'Design':
                router.push(buildProjectUrl(projectId, 'architecture/design'));
                break;
            case 'Interfaces':
                router.push(buildProjectUrl(projectId, 'architecture/interfaces'));
                break;
            default:
                break;
        }
    };

    return (
        <div className="flex flex-col h-full overflow-hidden">
            {/* Header - no tabs needed, just title */}
            <div className="sticky top-0 z-10 w-full bg-white border-b border-gray-200">
                <div className="flex items-center justify-center h-8 px-4">
                    <div className="flex items-center gap-2">
                        <ListTodo className="w-4 h-4 text-gray-600" />
                        <span className="typography-body-sm font-weight-medium text-gray-900">Project Progress</span>
                    </div>
                </div>
            </div>
            
            {/* Content area - always show Timeline */}
            <div className="flex-1 overflow-hidden">
                <div className="h-full overflow-y-auto scrollbar-hide w-full" id="projectTimeline">
                    {isLoading ? (
                        <TimelineSkeleton />
                    ) : (
                        <ProjectTimeline
                            projectStatus={projectStatus}
                            onRedirect={onRedirect}
                            refetchData={() => {
                                setRefresh(!refresh);
                                setProjectStatus({});
                            }}
                            isAutoConfigInProgress={isAutoConfigInProgress}
                        />
                    )}
                </div>
            </div>
        </div>
    );
};

export default CollapseViewer;
