import React, { useState, useContext, useEffect } from 'react';
import { StateContext } from './Context/StateContext';
import { ExecutionContext } from './Context/ExecutionContext';
import { getFlowlineStatus } from '@/utils/api';
import ProjectTimeline from './ProjectTimeline';
import { useRouter, usePathname } from "next/navigation";
import { ListTodo, RefreshCw, Clock, CheckCircle, AlertCircle, Circle } from 'lucide-react';

const CollapseViewer = () => {
    const { projectStatus, setProjectStatus, isAutoConfigInProgress } = useContext(ExecutionContext);
    const [isLoading, setIsLoading] = useState(false);
    const [refresh, setRefresh] = useState(false);
    const router = useRouter();
    const pathname = usePathname();

    // Extract project ID from pathname
    const projectId = pathname.split('/')[3];

    // Fetch project status
    useEffect(() => {
        const fetchProjectStatus = async () => {
            if (!projectId) return;

            setIsLoading(true);
            try {
                const response = await getFlowlineStatus(projectId);
                if (response && response.data) {
                    setProjectStatus(response.data);
                }
            } catch (error) {
                console.error('Error fetching project status:', error);
            } finally {
                setIsLoading(false);
            }
        };

        fetchProjectStatus();
    }, [projectId, refresh, setProjectStatus]);

    // Get progress summary
    const getProgressSummary = () => {
        if (!projectStatus) return { completed: 0, total: 0, percentage: 0 };

        const statuses = [
            projectStatus?.project_assets?.overall,
            projectStatus?.project_setup,
            projectStatus?.requirement?.overall,
            projectStatus?.architecture?.overall,
            projectStatus?.codegen_status
        ].filter(Boolean);

        const completed = statuses.filter(status => status === 'completed').length;
        const total = statuses.length;
        const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;

        return { completed, total, percentage };
    };

    const { completed, total, percentage } = getProgressSummary();

    // Modern loading skeleton
    const LoadingSkeleton = () => (
        <div className="p-4 space-y-4">
            {[1, 2, 3, 4, 5].map((item) => (
                <div key={item} className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse"></div>
                    <div className="flex-1 space-y-2">
                        <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                        <div className="h-3 bg-gray-100 rounded w-3/4 animate-pulse"></div>
                    </div>
                    <div className="w-6 h-6 bg-gray-200 rounded animate-pulse"></div>
                </div>
            ))}
        </div>
    );

    const onRedirect = (action) => {
        const { buildProjectUrl } = require('@/utils/navigationHelpers');
        const organizationId = pathname.split('/')[1];
        const type = pathname.split('/')[2];

        switch (action) {
            case 'Add Repo':
                router.push(`${pathname}?ProjectAsset=code`);
                break;
            case 'Add Doc':
                router.push(`${pathname}?ProjectAsset=documents`);
                break;
            case 'SystemContext':
                router.push(buildProjectUrl(projectId, 'architecture/system-context'));
                break;
            case 'Container':
                router.push(buildProjectUrl(projectId, 'architecture/container'));
                break;
            case 'Component':
                router.push(buildProjectUrl(projectId, 'architecture/component'));
                break;
            case 'Design':
                router.push(buildProjectUrl(projectId, 'architecture/design'));
                break;
            case 'Interfaces':
                router.push(buildProjectUrl(projectId, 'architecture/interfaces'));
                break;
            default:
                break;
        }
    };

    return (
        <div className="flex flex-col h-full bg-white">
            {/* Modern Header with Progress */}
            <div className="flex-none border-b border-gray-100 bg-white">
                <div className="p-4">
                    <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-2">
                            <ListTodo className="w-5 h-5 text-primary-600" />
                            <h2 className="text-sm font-semibold text-gray-900">Project Progress</h2>
                        </div>
                        <button
                            onClick={() => {
                                setRefresh(!refresh);
                                setProjectStatus({});
                            }}
                            className="p-1.5 hover:bg-gray-100 rounded-md transition-colors"
                            title="Refresh progress"
                        >
                            <RefreshCw className={`w-4 h-4 text-gray-500 ${isLoading ? 'animate-spin' : ''}`} />
                        </button>
                    </div>

                    {/* Progress Bar */}
                    <div className="space-y-2">
                        <div className="flex items-center justify-between text-xs">
                            <span className="text-gray-600">{completed} of {total} completed</span>
                            <span className="font-medium text-gray-900">{percentage}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                                className="bg-primary-600 h-2 rounded-full transition-all duration-500 ease-out"
                                style={{ width: `${percentage}%` }}
                            ></div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Content Area */}
            <div className="flex-1 overflow-hidden">
                <div className="h-full overflow-y-auto">
                    {isLoading ? (
                        <LoadingSkeleton />
                    ) : (
                        <ProjectTimeline
                            projectStatus={projectStatus}
                            onRedirect={onRedirect}
                            refetchData={() => {
                                setRefresh(!refresh);
                                setProjectStatus({});
                            }}
                            isAutoConfigInProgress={isAutoConfigInProgress}
                        />
                    )}
                </div>
            </div>
        </div>
    );
};

export default CollapseViewer;
